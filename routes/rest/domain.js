/* eslint-disable default-case */
/* eslint-disable consistent-return */
/* eslint-disable no-restricted-syntax */
/* eslint-disable max-len */
/* eslint-disable import/no-extraneous-dependencies */
const {
  Route53DomainsClient, CheckDomainAvailabilityCommand, ListPricesCommand, GetDomainSuggestionsCommand,
  GetDomainDetailCommand,
  RegisterDomainCommand,
  ListDomainsCommand,
  EnableDomainAutoRenewCommand,
  DisableDomainAutoRenewCommand,
  ResendContactReachabilityEmailCommand
} = require("@aws-sdk/client-route-53-domains")
const {
  ACMClient, DescribeCertificateCommand, RequestCertificateCommand, ListCertificatesCommand, DeleteCertificateCommand
} = require("@aws-sdk/client-acm")
const { CloudFrontClient, CreateDistributionCommand, ListDistributionsCommand } = require("@aws-sdk/client-cloudfront")
const {
  Route53Client, ChangeResourceRecordSetsCommand, ListResourceRecordSetsCommand, ListHostedZonesCommand
} = require("@aws-sdk/client-route-53")
// const { WAFClient } = require("@aws-sdk/client-waf")

const moment = require("moment")
const isValidDomain = require("is-valid-domain")
const dns = require("dns").promises
const axios = require("axios")
const {
  User, Platform, Payment, Subscription
} = require("../../models")
const stripeService = require("../../lib/stripe")

const { validTlsList } = require("../../lib/validTld")

const accessKeyId = process.env.AWS_ACCESS_KEY
const secretAccessKey = process.env.AWS_SECRET_KEY
const region = process.env.AWS_BUCKET_REGION

const client = new Route53DomainsClient({ // For Credentails Set Up
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  region
})

const clientACM = new ACMClient({
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  region
})

const clientCloudFront = new CloudFrontClient({
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  region
})

const clientRoute53 = new Route53Client({
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  region
})
// Initialize WAF client if needed in the future
// const clientWAF = new WAFClient({
//   credentials: {
//     accessKeyId,
//     secretAccessKey,
//   },
//   region
// })

const extractBaseDomain = (input) => {
  if (typeof input !== "string") {
    throw new Error("Domain must be a string")
  }

  // Remove whitespace and convert to lowercase
  let cleaned = input.trim().replace(/\s+/g, "").toLowerCase()

  // Remove protocol if present
  cleaned = cleaned.replace(/^(https?:\/\/)?(www\.)?/, "")

  // Split by dots
  const parts = cleaned.split(".")

  if (parts.length < 2) {
    // Add `.com` if only base name like 'bhaskardey'
    cleaned += ".com"
  } else {
    // Keep only base domain (e.g., bhaskardey.com from app.bhaskardey.com)
    cleaned = parts.slice(-2).join(".")
  }

  // Validate domain
  const domainRegex = /^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/
  if (!domainRegex.test(cleaned)) {
    throw new Error("Invalid domain format")
  }

  return cleaned
}

// List of supported domain providers
const domainProviders = [
  { id: "godaddy", name: "GoDaddy", website: "https://www.godaddy.com" },
  { id: "namecheap", name: "Namecheap", website: "https://www.namecheap.com" },
  { id: "googledomains", name: "Google Domains", website: "https://domains.google" },
  { id: "cloudflare", name: "Cloudflare", website: "https://www.cloudflare.com" },
  { id: "route53", name: "AWS Route 53", website: "https://aws.amazon.com/route53" },
  { id: "hostgator", name: "HostGator", website: "https://www.hostgator.com" },
  { id: "bluehost", name: "Bluehost", website: "https://www.bluehost.com" },
  { id: "dreamhost", name: "DreamHost", website: "https://www.dreamhost.com" },
  { id: "other", name: "Other Provider", website: null }
]

// Server IP address for A records
const SERVER_IP_ADDRESS = process.env.SERVER_IP_ADDRESS || "***********"

module.exports = {
  /**
   * @swagger
   * /domain/initiate-purchase:
   *   post:
   *     summary: Initiate domain purchase payment
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - domainName
   *               - platformId
   *               - domainPrice
   *             properties:
   *               domainName:
   *                 type: string
   *                 description: The domain name to purchase
   *                 example: "example.com"
   *               platformId:
   *                 type: string
   *                 description: ID of the platform for which the domain is being purchased
   *                 example: "60d21b4667d0d8992e610c85"
   *               domainPrice:
   *                 type: number
   *                 description: Price of the domain in USD
   *                 example: 12.99
   *               durationInYears:
   *                 type: number
   *                 description: Number of years to register the domain for (defaults to 1)
   *                 example: 1
   *               autoRenew:
   *                 type: boolean
   *                 description: Whether to enable auto-renewal for the domain (defaults to true)
   *                 example: true
   *     responses:
   *       200:
   *         description: Domain purchase payment initiated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     payment:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         amount:
   *                           type: number
   *                           example: 12.99
   *                         status:
   *                           type: string
   *                           example: "pending"
   *                         paymentIntentId:
   *                           type: string
   *                           example: "pi_1234567890"
   *                     clientSecret:
   *                       type: string
   *                       description: The client secret to be used for payment confirmation on the client side
   *                       example: "pi_1234567890_secret_5678"
   *       400:
   *         description: Bad request - missing parameters or no default payment method
   *       404:
   *         description: User or platform not found
   *       500:
   *         description: Server error
   */
  async initiateDomainPurchase(req, res) {
    try {
      const userId = req.user.id
      const {
        domainName, platformId, domainPrice, durationInYears = 1, autoRenew = true
      } = req.body

      // Validate required fields
      if (!domainName || !platformId || !domainPrice) {
        return res.status(400).json({
          error: true,
          reason: "domainName, platformId, and domainPrice are required"
        })
      }

      if (!isValidDomain(domainName)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid domain format"
        })
      }

      // Get user
      const user = await User.findOne({ _id: userId }).exec()
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Verify platform exists
      const platform = await Platform.findOne({ _id: platformId }).exec()
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Create or get Stripe customer
      let stripeId = user.stripeId && user.stripeId !== "" ? user.stripeId : null
      if (!stripeId) {
        const customer = await stripeService.createAccount(user.email)
        stripeId = customer.id
        user.stripeId = stripeId
        await user.save()
      }

      // Fetch default payment method
      const defaultCard = await stripeService.fetchDefaultCard(stripeId)
      if (!defaultCard) {
        return res.status(400).json({
          error: true,
          reason: "No default payment method found. Please add a card."
        })
      }

      // Check if user has an active subscription
      const subscription = await Subscription.findOne({
        user: userId,
        _platform: platformId,
        status: "active"
      })

      // Process domain payment
      const description = `Domain purchase: ${domainName} (${durationInYears} year${durationInYears > 1 ? "s" : ""})`
      const paymentIntent = await stripeService.createPaymentIntent(
        stripeId,
        domainPrice,
        description
      )

      // Create payment record
      const payment = await Payment.create({
        _user: userId,
        _platform: platformId,
        amount: domainPrice,
        currency: "USD",
        status: "pending",
        paymentIntentId: paymentIntent.id,
        paymentFor: "domain",
        metadata: {
          domainName,
          durationInYears,
          autoRenew
        },
        _subscription: subscription ? subscription._id : null
      })

      return res.json({
        error: false,
        data: {
          payment,
          clientSecret: paymentIntent.client_secret,
          defaultCard
        }
      })
    } catch (error) {
      console.error("Domain payment error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while processing your payment"
      })
    }
  },

  /**
 * @swagger
 * /domains/search:
 *   post:
 *     summary: Check domain availability and get suggestions
 *     description: Checks the availability of a domain and provides suggestions for similar domains.
 *     tags: [Domain]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               domain:
 *                 type: string
 *                 description: The domain name to check availability for.
 *                 example: "example"
 *               number:
 *                 type: number
 *                 description: Number of domain suggestions to retrieve.
 *                 example: 10
 *           example:
 *             domain: "example"
 *             number: 10
 *     responses:
 *       200:
 *         description: Domain availability and suggestions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 data:
 *                   type: object
 *                   properties:
 *                     exactMatch:
 *                       type: array
 *                       description: List of exact match domain availability and pricing details.
 *                       items:
 *                         type: object
 *                         properties:
 *                           Availability:
 *                             type: string
 *                             description: Availability status of the domain.
 *                             example: "AVAILABLE"
 *                           DomainName:
 *                             type: string
 *                             description: The exact domain name.
 *                             example: "example.com"
 *                           RegistrationPrice:
 *                             type: number
 *                             description: Price for registering the domain.
 *                             example: 12.99
 *                           TransferPrice:
 *                             type: number
 *                             description: Price for transferring the domain.
 *                             example: 10.99
 *                           RenewalPrice:
 *                             type: number
 *                             description: Price for renewing the domain.
 *                             example: 14.99
 *                     suggestDomain:
 *                       type: array
 *                       description: List of suggested domain names with availability and pricing details.
 *                       items:
 *                         type: object
 *                         properties:
 *                           Availability:
 *                             type: string
 *                             description: Availability status of the suggested domain.
 *                             example: "AVAILABLE"
 *                           DomainName:
 *                             type: string
 *                             description: The suggested domain name.
 *                             example: "example.net"
 *                           RegistrationPrice:
 *                             type: number
 *                             description: Price for registering the suggested domain.
 *                             example: 11.99
 *                           TransferPrice:
 *                             type: number
 *                             description: Price for transferring the suggested domain.
 *                             example: 9.99
 *                           RenewalPrice:
 *                             type: number
 *                             description: Price for renewing the suggested domain.
 *                             example: 13.99
 *       400:
 *         description: Bad request - missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Domain is required"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 reason:
 *                   type: string
 *                   example: "Internal server error"
 */
  async availableDomains(req, res) {
    try {
      const { domain, number } = req.body
      if (domain === undefined) {
        return res.status(400).json({ error: true, message: "Domain is required" })
      }
      if (!isValidDomain(domain)) {
        return res.status(400).json({ error: true, message: "Domain is invalid" })
      }
      let modifiedDoamin = domain.replace(/\s+/g, "").toLowerCase()

      if (modifiedDoamin.split(".").at(1) === undefined) {
        modifiedDoamin = `${modifiedDoamin}.com`
      }
      const domainInput = {
        DomainName: modifiedDoamin, // required
      }

      const domainArrRes = {
        exactMatch: [],
        suggestDomain: []
      }

      // Calling Aws API Endpoint
      const domainCommand = new CheckDomainAvailabilityCommand(domainInput)
      const domainResponse = await client.send(domainCommand)

      if (domainResponse.Availability !== "AVAILABLE") {
        domainArrRes.exactMatch.push({ Availability: domainResponse.Availability, DomainName: modifiedDoamin })
      }

      if (domainResponse.Availability === "AVAILABLE") {
        const priceInput = {
          Tld: `${modifiedDoamin.split(".").at(1)}`,
        }

        const priceCommand = new ListPricesCommand(priceInput)
        const priceResponse = await client.send(priceCommand)

        priceResponse.Prices.forEach((element) => {
          if (modifiedDoamin.split(".").at(1) === element.Name) {
            domainArrRes.exactMatch.push({ Availability: domainResponse.Availability, DomainName: modifiedDoamin, ...element })
          }
        })
      }

      const count = number === undefined ? 10 : Number(number)

      const suggestDomainInput = {
        DomainName: modifiedDoamin, // ex. abcde.com
        SuggestionCount: count,
        OnlyAvailable: true
      }

      const suggestDomainCommand = new GetDomainSuggestionsCommand(suggestDomainInput)
      const domainSuggestResponse = await client.send(suggestDomainCommand)

      for (let i = 0; i < domainSuggestResponse.SuggestionsList.length; i += 1) {
        const param = {
          Tld: `${domainSuggestResponse.SuggestionsList[i].DomainName.split(".").at(1)}`,
        }
        const commandResponse = new ListPricesCommand(param)
        // eslint-disable-next-line no-await-in-loop
        const suggestDomaiin = await client.send(commandResponse)

        suggestDomaiin.Prices.forEach((element) => {
          if (domainSuggestResponse.SuggestionsList[i].DomainName.split(".").at(1) === element.Name) {
            domainArrRes.suggestDomain.push({ Availability: domainSuggestResponse.SuggestionsList[i].Availability, DomainName: domainSuggestResponse.SuggestionsList[i].DomainName, ...element })
          }
        })
      }

      return res.status(200).json({ error: false, data: domainArrRes })
    } catch (error) {
      console.log(`time[${moment().format("DD/MM/YYYY HH:ss")}]<http://localhost:3000/api/v1/domains (post)> ->`, error || error.message)
      return res.status(500).json({ error: true, reason: error.message })
    }
  },
  /**
 * @swagger
 * /tld/prices:
 *   post:
 *     summary: Get TLD prices
 *     description: Retrieve the prices for specific TLDs or a list of available TLDs.
 *     tags: [Domain]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tld:
 *                 type: string
 *                 description: The TLD to retrieve prices for (e.g., ".com").
 *                 example: ".com"
 *               maxNumber:
 *                 type: number
 *                 description: Maximum number of TLDs to retrieve.
 *                 example: 10
 *               maker:
 *                 type: string
 *                 description: Pagination marker for retrieving the next set of results.
 *                 example: "marker123"
 *           example:
 *             tld: ".com"
 *             maxNumber: 10
 *             maker: "marker123"
 *     responses:
 *       200:
 *         description: TLD prices retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 data:
 *                   type: object
 *                   properties:
 *                     Prices:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           Name:
 *                             type: string
 *                             description: The name of the TLD.
 *                             example: ".com"
 *                           RegistrationPrice:
 *                             type: number
 *                             description: The price for registering the TLD.
 *                             example: 12.99
 *                           TransferPrice:
 *                             type: number
 *                             description: The price for transferring the TLD.
 *                             example: 10.99
 *                           RenewalPrice:
 *                             type: number
 *                             description: The price for renewing the TLD.
 *                             example: 14.99
 *                     NextMarker:
 *                       type: string
 *                       description: Marker for the next set of results.
 *                       example: "nextMarker123"
 *       400:
 *         description: Bad request - invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "TLD is not valid"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 reason:
 *                   type: string
 *                   example: "Internal server error"
 */
  async getTldPrices(req, res) {
    try {
      const { tld = "", maxNumber = 10, maker = "" } = req.body
      const query = {}
      if (typeof maxNumber !== "number") {
        return res.status(400).json({ error: true, message: "maxNumber should be a number" })
      }
      if (maxNumber < 0) {
        return res.status(400).json({ error: true, message: "maxNumber should be greater than 0" })
      }
      if (typeof maxNumber === "number" && maxNumber > 0) {
        query.MaxItems = Math.abs(Math.floor(maxNumber))
      }
      if (maker !== "") {
        query.Maker = maker
      }
      if (tld !== "") {
        if (!tld.startsWith(".")) {
          return res.status(400).json({ error: true, message: "TLD should start with ." })
        }
        if (!validTlsList.includes(tld.replace(".", "").toUpperCase())) {
          return res.status(400).json({ error: true, message: "TLD is not valid" })
        }
        query.Tld = tld.replace(".", "")
      }
      const command = new ListPricesCommand(query)
      const response = await client.send(command)
      return res.status(200).json({ error: false, data: response })
    } catch (error) {
      console.log(`time[${moment().format("DD/MM/YYYY HH:ss")}]<http://localhost:3000/aws/check/domain (post)> ->`, error || error.message)
      return res.status(500).json({ error: true, reason: error.message })
    }
  },
  /**
 * @swagger
 * /domain/details:
 *   post:
 *     summary: Get domain details
 *     description: Retrieves domain information using a domain registrar API (e.g., AWS Route53).
 *     tags: [Domain]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - domain
 *             properties:
 *               domain:
 *                 type: string
 *                 description: The full domain name to retrieve details for.
 *                 example: "example.com"
 *     responses:
 *       200:
 *         description: Domain details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 data:
 *                   type: object
 *                   description: The response from the domain detail API.
 *       400:
 *         description: Bad request (missing domain or internal error).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Domain is required"
 */

  async domainDetails(req, res) {
    try {
      const { domain } = req.body
      if (domain === undefined) {
        return res.status(400).json({ error: true, message: "Domain is required" })
      }
      if (!isValidDomain(domain)) {
        return res.status(400).json({ error: true, message: "Domain is not valid" })
      }

      const modifiedDomain = extractBaseDomain(domain)

      const input = {
        DomainName: modifiedDomain
      }
      const command = new GetDomainDetailCommand(input)
      const response = await client.send(command)
      return res.status(200).json({ error: false, data: response })
    } catch (error) {
      return res.status(400).json({ error: true, message: error.message })
    }
  },
  /**
 * @swagger
 * /resend/confirmation/email:
 *   post:
 *     summary: List domain validity
 *     description: Returns a list of valid and expired domains filtered by domain name if provided.
 *     operationId: listDomainValidity
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               domain:
 *                 type: string
 *                 description: Optional domain name prefix to filter the list.
 *                 example: "example"
 *     responses:
 *       200:
 *         description: Successfully retrieved lists of valid and expired domains.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 expiredDomainList:
 *                   type: array
 *                   description: Domains that have already expired.
 *                   items:
 *                     type: object
 *                 validDomainList:
 *                   type: array
 *                   description: Domains that are still valid.
 *                   items:
 *                     type: object
 *       400:
 *         description: Bad request or internal error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: true
 *     tags:
 *       - Domain
 */

  async resendConfirmationEmail(req, res) {
    try {
      const { domain } = req.body
      if (domain === undefined) {
        return res.status(400).json({ error: true, message: "Domain is required" })
      }
      if (!isValidDomain(domain)) {
        return res.status(400).json({ error: true, message: "Domain is not valid" })
      }
      const input = {
        domainName: domain
      }
      const command = new ResendContactReachabilityEmailCommand(input)
      const response = await client.send(command)
      return res.status(200).json({ error: false, data: response })
    } catch (error) {
      console.log(`time[${moment().format("DD/MM/YYYY HH:ss")}]`, error || error.message)
      return res.status(500).json({ error: true, reason: error.message })
    }
  },

  /**
   * @swagger
   * /list/domain/validity:
   *   post:
   *     summary: List domain validity
   *     description: Returns a list of valid and expired domains filtered by domain name if provided.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               domain:
   *                 type: string
   *                 description: Optional domain name prefix to filter the list.
   *                 example: "example"
   *     responses:
   *       200:
   *         description: Successfully retrieved lists of valid and expired domains.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 expiredDomainList:
   *                   type: array
   *                   description: Domains that have already expired.
   *                   items:
   *                     type: object
   *                     properties:
   *                       DomainName:
   *                         type: string
   *                         example: "example.com"
   *                       Expiry:
   *                         type: string
   *                         format: date-time
   *                         example: "2023-01-01T00:00:00Z"
   *                 validDomainList:
   *                   type: array
   *                   description: Domains that are still valid.
   *                   items:
   *                     type: object
   *                     properties:
   *                       DomainName:
   *                         type: string
   *                         example: "example.net"
   *                       Expiry:
   *                         type: string
   *                         format: date-time
   *                         example: "2025-01-01T00:00:00Z"
   *       400:
   *         description: Bad request or internal error occurred.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *       500:
   *         description: Server error
   */
  async listDomainValidity(req, res) {
    try {
      const { domain } = req.body
      const params = {}
      if (domain) {
        if (!isValidDomain(domain)) {
          return res.status(400).json({ error: true })
        }
        params.FilterConditions = [
          {
            Name: "DomainName",
            Operator: "BEGINS_WITH",
            Values: [domain]
          }
        ]
      }
      const command = new ListDomainsCommand(params)
      const response = await client.send(command)

      // check if domain expired or not
      const expiredDomainList = []
      const validDomainList = []
      response.Domains.forEach((element) => {
        const date = moment(element.Expiry)
        const today = moment()
        const diff = date.diff(today, "days")
        if (diff < 0) {
          expiredDomainList.push(element)
        } else {
          validDomainList.push(element)
        }
      })

      return res.status(200).json({ error: false, expiredDomainList, validDomainList })
    } catch (error) {
      console.log(error)
      return res.status(400).json({ error: true })
    }
  },
  /**
   * @swagger
   * /update/renewal:
   *   post:
   *     summary: Update domain auto-renewal setting
   *     description: Enable or disable auto-renewal for a domain.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - domain
   *               - AutoRenew
   *             properties:
   *               domain:
   *                 type: string
   *                 description: The domain name to update auto-renewal for.
   *                 example: "example.com"
   *               AutoRenew:
   *                 type: boolean
   *                 description: Whether to enable (true) or disable (false) auto-renewal.
   *                 example: true
   *     responses:
   *       200:
   *         description: Auto-renewal setting updated successfully.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 response:
   *                   type: object
   *                   description: Response from the domain registrar API.
   *       400:
   *         description: Bad request - missing or invalid input.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "Domain is required"
   */
  async updateAutoRenewal(req, res) {
    try {
      const { domain, AutoRenew } = req.body
      if (domain === undefined) {
        return res.status(400).json({ error: true, message: "Domain is required" })
      }
      if (!isValidDomain(domain)) {
        return res.status(400).json({ error: true, message: "Domain is not valid" })
      }
      if (AutoRenew === true) {
        const enableCommand = new EnableDomainAutoRenewCommand({
          DomainName: domain
        })
        const response = await client.send(enableCommand)
        return res.status(200).json({ error: false, response })
      }
      const disableCommand = new DisableDomainAutoRenewCommand({
        DomainName: domain
      })
      const response = await client.send(disableCommand)
      return res.status(200).json({ error: false, response })
    } catch (error) {
      return res.status(400).json({ error: true })
    }
  },

  /**
   * @swagger
   * /domain/setup:
   *   post:
   *     summary: Set up a domain connection
   *     description: Connect an existing domain to the platform by providing domain name and provider. This is the first step in the domain connection process for Option 1 (Connect Existing Domain).
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - provider
   *             properties:
   *               name:
   *                 type: string
   *                 description: The domain name to connect.
   *                 example: "example.com"
   *               provider:
   *                 type: string
   *                 description: The ID of the domain provider.
   *                 example: "godaddy"
   *               platformId:
   *                 type: string
   *                 description: The ID of the platform to connect the domain to. If not provided, the user's primary platform will be used.
   *                 example: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Domain setup initiated successfully. Returns DNS records that need to be added to the domain provider.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     platformId:
   *                       type: string
   *                       description: The ID of the platform the domain is connected to.
   *                       example: "60d21b4667d0d8992e610c85"
   *                     domain:
   *                       type: object
   *                       properties:
   *                         name:
   *                           type: string
   *                           example: "example.com"
   *                         provider:
   *                           type: string
   *                           example: "godaddy"
   *                         verified:
   *                           type: boolean
   *                           example: false
   *                         verificationToken:
   *                           type: string
   *                           example: "vewmee-verification=example.com-kz5xty"
   *                         dnsRecords:
   *                           type: array
   *                           items:
   *                             type: object
   *                             properties:
   *                               recordType:
   *                                 type: string
   *                                 example: "TXT"
   *                               host:
   *                                 type: string
   *                                 example: "_verify.example.com"
   *                               value:
   *                                 type: string
   *                                 example: "vewmee-verification=example.com-kz5xty"
   *                               ttl:
   *                                 type: number
   *                                 example: 3600
   *                               purpose:
   *                                 type: string
   *                                 example: "Verifies domain ownership"
   *                               verified:
   *                                 type: boolean
   *                                 example: false
   *                     launchStatus:
   *                       type: number
   *                       description: The current launch status of the platform. 3 indicates domain setup stage.
   *                       example: 3
   *                     providerInstructions:
   *                       type: object
   *                       description: Provider-specific instructions for adding DNS records.
   *                       properties:
   *                         title:
   *                           type: string
   *                           example: "How to add DNS records in GoDaddy"
   *                         steps:
   *                           type: array
   *                           items:
   *                             type: string
   *                             example: "Log in to your GoDaddy account"
   *       400:
   *         description: Bad request - missing or invalid input.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Domain name is required"
   *       404:
   *         description: User or platform not found.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Platform not found or you don't have access to it"
   *       500:
   *         description: Server error.
   */
  async setup(req, res) {
    try {
      const userId = req.user.id
      const { name, provider, platformId } = req.body

      // Validate required fields
      if (!name) {
        return res.status(400).json({
          error: true,
          reason: "Domain name is required"
        })
      }

      if (!isValidDomain(name)) {
        return res.status(400).json({
          error: true,
          reason: "Domain name is not valid"
        })
      }

      if (!provider) {
        return res.status(400).json({
          error: true,
          reason: "Domain provider is required"
        })
      }

      // Validate domain format
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
      if (!domainRegex.test(name)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid domain format"
        })
      }

      // Get user
      const user = await User.findOne({ _id: userId })
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get or create platform
      let platform = null

      // If platformId is provided, try to find that specific platform
      if (platformId) {
        // Validate that the platform exists and the user has access to it
        platform = await Platform.findOne({
          _id: platformId,
          $or: [
            { primaryUser: userId },
            { teamMembers: userId }
          ]
        })

        if (!platform) {
          return res.status(404).json({
            error: true,
            reason: "Platform not found or you don't have access to it"
          })
        }
      } else if (user.platforms && user.platforms.length > 0) {
        // If no platformId is provided, try to find the user's primary platform
        platform = await Platform.findOne({
          primaryUser: userId,
          _id: { $in: user.platforms }
        })
      }

      // If no platform was found or specified, create a new one
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
        // Create new platform for this user
        // platform = new Platform({
        //   user: userId,
        //   primaryUser: userId
        // })

        // // Update user with reference to the new platform
        // if (!user.platforms) {
        //   user.platforms = []
        // }
        // user.platforms.push(platform._id)
        // await user.save()
      }
      // Check if verification token already exists for this domain
      let verificationToken
      const recordName = `_verify.${name}`
      let dnsRecords = []

      // If domain and verification token already exist, use the existing token
      if (platform.domain && platform.domain.verificationToken) {
        console.log("Using existing verification token")
        verificationToken = platform.domain.verificationToken
      } else {
        // Create a new verification token
        console.log("Creating new verification token")
        verificationToken = `vewmee-verification=${name}-${Date.now().toString(36)}`
      }

      // Define required DNS records
      dnsRecords = [
        {
          recordType: "TXT",
          host: recordName,
          value: verificationToken,
          ttl: 3600,
          purpose: "Verifies domain ownership",
          verified: false
        }
      ]

      // Update platform with domain information
      platform.domain = {
        name,
        provider,
        verified: false,
        dnsRecords,
        verificationToken
      }
      // Update launch status to domain stage (3) if currently at a lower stage
      // if (platform.launchStatus < 3) {
      //   platform.launchStatus = 3
      // }
      console.log("platform ===", platform)
      await platform.save()

      // Provider-specific instructions
      const providerObj = domainProviders.find((p) => p.id === provider)
      const providerInstructions = {
        title: `How to add DNS records in ${providerObj ? providerObj.name : provider}`,
        steps: [
          `Log in to your ${providerObj ? providerObj.name : provider} account`,
          `Navigate to the DNS management section for ${name}`,
          "Add each of the DNS records listed below",
          "Save your changes",
          "Wait for DNS propagation (can take up to 48 hours)"
        ]
      }

      return res.json({
        error: false,
        data: {
          platformId: platform._id,
          domain: platform.domain,
          launchStatus: platform.launchStatus,
          providerInstructions
        }
      })
    } catch (error) {
      console.log("error", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /domain/verify/{site}:
   *   post:
   *     summary: Verify domain DNS records
   *     description: Verifies domain ownership by checking for a specific TXT record at _verify.domain.com. This is the second step in the domain connection process for Option 1 (Connect Existing Domain). Upon successful verification, adds standard DNS records for the domain.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: site
   *         required: true
   *         schema:
   *           type: string
   *         description: The domain name to verify
   *         example: "example.com"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - platformId
   *             properties:
   *               platformId:
   *                 type: string
   *                 description: The ID of the platform to verify the domain for.
   *                 example: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Domain verification check completed.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     domain:
   *                       type: object
   *                       properties:
   *                         name:
   *                           type: string
   *                           example: "example.com"
   *                         provider:
   *                           type: string
   *                           example: "godaddy"
   *                         verified:
   *                           type: boolean
   *                           example: true
   *                         dnsRecords:
   *                           type: array
   *                           items:
   *                             type: object
   *                             properties:
   *                               recordType:
   *                                 type: string
   *                                 example: "A"
   *                               host:
   *                                 type: string
   *                                 example: "@"
   *                               value:
   *                                 type: string
   *                                 example: "***********"
   *                               ttl:
   *                                 type: number
   *                                 example: 3600
   *                               purpose:
   *                                 type: string
   *                                 example: "Points your domain to our server"
   *                               verified:
   *                                 type: boolean
   *                                 example: false
   *                     isVerified:
   *                       type: boolean
   *                       description: Whether the domain has been verified successfully.
   *                       example: true
   *                     message:
   *                       type: string
   *                       description: A user-friendly message about the verification status.
   *                       example: "Domain verification successful"
   *       400:
   *         description: Bad request - missing domain parameter.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "Domain is required"
   *       404:
   *         description: User, platform, or domain not found.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Domain not found"
   *       500:
   *         description: Server error.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   */

  async verify(req, res) {
    const { site } = req.params
    if (site === undefined) {
      return res.status(400).json({ error: true, message: "Domain is required" })
    }

    try {
      const userId = req.user.id
      const { platformId } = req.body

      // Get user
      const user = await User.findOne({ _id: userId })
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get primary platform
      if (!user.platforms || user.platforms.length === 0) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      const platform = await Platform.findOne({
        primaryUser: userId,
        _id: platformId
      })

      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      const { domain } = platform
      if (!domain || !domain.name) {
        return res.status(404).json({
          error: true,
          reason: "Domain not found"
        })
      }

      // let allVerified = true

      let txtRecords = []
      try {
        txtRecords = await dns.resolveTxt(`_verify.${domain.name}`)
      } catch (err) {
        if (err.code === "ENOTFOUND") {
          // Domain or record doesn't exist — handle gracefully
          return res.status(200).json({
            error: true,
            reason: `DNS TXT record for _verify.${domain.name} not found`
          })
        }
        throw err // let unexpected errors bubble up
      }

      console.log("txtRecords", txtRecords)
      const allRecords = txtRecords.flat().join("")
      console.log("allRecords", allRecords)
      console.log("allRecords.includes(domain.verificationToken)", allRecords.includes(domain.verificationToken))

      const isVerified = allRecords.includes(domain.verificationToken)

      // Update verification status for each record (simulated)
      // domain.dnsRecords = domain.dnsRecords.map((record) => {
      //   // Simulate 80% chance of successful verification
      //   const verified = Math.random() > 0.2
      //   if (!verified) {
      //     allVerified = false
      //   }
      //   return { ...record, verified }
      // })

      // Add required DNS records for the domain
      // domain.dnsRecords = [
      //   {
      //     recordType: "A",
      //     host: "@",
      //     value: process.env.SERVER_IP_ADDRESS, // Example IP, would be replaced with actual server IP
      //     verified: false,
      //     ttl: 3600,
      //     purpose: "Points your domain to our server"
      //   },
      //   {
      //     recordType: "CNAME",
      //     host: "www",
      //     value: domain.name,
      //     verified: false,
      //     ttl: 3600,
      //     purpose: `Makes www.${domain.name} work`
      //   },
      //   {
      //     recordType: "CNAME",
      //     host: "fan",
      //     value: domain.name,
      //     verified: false,
      //     ttl: 3600,
      //     purpose: "Sets up the fan app subdomain"
      //   },
      //   {
      //     recordType: "CNAME",
      //     host: "creator",
      //     value: domain.name,
      //     verified: false,
      //     ttl: 3600,
      //     purpose: "Sets up the creator app subdomain"
      //   }
      // ]

      // Update domain verification status
      domain.verified = isVerified

      // Save changes
      platform.domain = domain
      await platform.save()

      // If domain is verified, generate URLs
      // let urls = null
      // if (allVerified) {
      //   // Generate URLs
      //   const websiteUrl = `https://${domainName}`
      //   const fanAppUrl = `https://fan.${domainName}`
      //   const creatorAppUrl = `https://creator.${domainName}`

      //   // Update platform with URLs
      //   platform.website = websiteUrl
      //   platform.fanAppLink = fanAppUrl
      //   platform.creatorLink = creatorAppUrl

      //   await platform.save()

      //   urls = {
      //     websiteUrl,
      //     fanAppUrl,
      //     creatorAppUrl
      //   }
      // }

      return res.json({
        error: false,
        data: {
          domain: platform.domain,
          isVerified,
          message: isVerified
            ? "Domain verification successful"
            : "Some DNS records are not verified yet. Please check your DNS settings."
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /domain/purchase:
   *   put:
   *     summary: Register Domain
   *     tags: [Domain]
   *     description: Register a new domain through AWS Route53 and store payment details in the payment schema
   *     produces:
   *       - application/json
   *
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - DomainName
   *               - RegistrantContact
   *             properties:
   *               DomainName:
   *                 type: string
   *                 description: The domain name to register
   *                 example: "example.com"
   *               DurationInYears:
   *                 type: number
   *                 description: Number of years to register the domain for (defaults to 1)
   *                 example: 1
   *               AutoRenew:
   *                 type: boolean
   *                 description: Whether to enable auto-renewal for the domain (defaults to true)
   *                 example: true
   *               domainPrice:
   *                 type: number
   *                 description: Price of the domain registration (defaults to 12.99)
   *                 example: 12.99
   *               AdminContact:
   *                 type: object
   *                 description: Administrative contact information (optional - defaults to Vineet Harbhajanka's contact information)
   *                 properties:
   *                   FirstName:
   *                     type: string
   *                     example: "Vineet"
   *                   LastName:
   *                     type: string
   *                     example: "Harbhajanka"
   *                   ContactType:
   *                     type: string
   *                     example: "PERSON"
   *                   AddressLine1:
   *                     type: string
   *                     example: "CG-84"
   *                   City:
   *                     type: string
   *                     example: "Kolkata"
   *                   State:
   *                     type: string
   *                     example: "WB"
   *                   CountryCode:
   *                     type: string
   *                     example: "IN"
   *                   ZipCode:
   *                     type: string
   *                     example: "700091"
   *                   PhoneNumber:
   *                     type: string
   *                     example: "+91.9007777974"
   *                   Email:
   *                     type: string
   *                     example: "<EMAIL>"
   *               RegistrantContact:
   *                 type: object
   *                 description: Registrant contact information (required)
   *                 properties:
   *                   FirstName:
   *                     type: string
   *                     example: "John"
   *                   LastName:
   *                     type: string
   *                     example: "Doe"
   *                   ContactType:
   *                     type: string
   *                     example: "PERSON"
   *                   AddressLine1:
   *                     type: string
   *                     example: "123 Main St"
   *                   City:
   *                     type: string
   *                     example: "New York"
   *                   State:
   *                     type: string
   *                     example: "NY"
   *                   CountryCode:
   *                     type: string
   *                     example: "US"
   *                   ZipCode:
   *                     type: string
   *                     example: "10001"
   *                   PhoneNumber:
   *                     type: string
   *                     example: "*************"
   *                   Email:
   *                     type: string
   *                     example: "<EMAIL>"
   *               TechContact:
   *                 type: object
   *                 description: Technical contact information (optional - defaults to Vineet Harbhajanka's contact information)
   *                 properties:
   *                   FirstName:
   *                     type: string
   *                   LastName:
   *                     type: string
   *                   ContactType:
   *                     type: string
   *                   AddressLine1:
   *                     type: string
   *                   City:
   *                     type: string
   *                   State:
   *                     type: string
   *                   CountryCode:
   *                     type: string
   *                   ZipCode:
   *                     type: string
   *                   PhoneNumber:
   *                     type: string
   *                   Email:
   *                     type: string
   *               PrivacyProtectAdminContact:
   *                 type: boolean
   *                 description: Whether to enable privacy protection for the admin contact (defaults to true)
   *                 example: true
   *               PrivacyProtectRegistrantContact:
   *                 type: boolean
   *                 description: Whether to enable privacy protection for the registrant contact (defaults to true)
   *                 example: true
   *               PrivacyProtectTechContact:
   *                 type: boolean
   *                 description: Whether to enable privacy protection for the technical contact (defaults to true)
   *                 example: true
   *
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Domain registered successfully and payment details stored
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     domainRegistration:
   *                       type: object
   *                       description: Response from AWS Route53 domain registration service
   *                       properties:
   *                         OperationId:
   *                           type: string
   *                           example: "a1b2c3d4-5678-90ab-cdef-EXAMPLE11111"
   *                     platform:
   *                       type: object
   *                       description: Updated platform information with the new domain
   *                       properties:
   *                         domain:
   *                           type: object
   *                           properties:
   *                             name:
   *                               type: string
   *                               example: "example.com"
   *                             provider:
   *                               type: string
   *                               example: "AWS"
   *                             verified:
   *                               type: boolean
   *                               example: false
   *                             dnsRecords:
   *                               type: array
   *                               items:
   *                                 type: object
   *                         launchStatus:
   *                           type: number
   *                           example: 3
   *       400:
   *         description: Bad request - missing or invalid input
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "DomainName is required or RegistrantContact is required"
   *       404:
   *         description: User not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   *       500:
   *         description: Internal Server Error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Error registering domain"
   */
  async purchase(req, res) {
    try {
      const {
        DomainName, AdminContact, RegistrantContact, DurationInYears = 1, AutoRenew = true, paymentIntentId
      } = req.body

      if (!DomainName) {
        return res.status(400).json({ error: true, message: "DomainName is required" })
      }
      if (isValidDomain(DomainName) === false) {
        return res.status(400).json({ error: true, message: "DomainName is not valid" })
      }
      if (!RegistrantContact) {
        return res.status(400).json({ error: true, message: "RegistrantContact is required" })
      }
      if (!paymentIntentId) {
        return res.status(400).json({ error: true, message: "paymentIntentId is required" })
      }

      // Verify payment was successful
      const payment = await Payment.findOne({ paymentIntentId }).exec()
      if (!payment) {
        return res.status(404).json({ error: true, message: "Payment not found" })
      }

      // if (payment.status !== "completed") {
      //   return res.status(400).json({ error: true, message: "Payment has not been completed" })
      // }

      // Use provided AdminContact or default to Vineet's contact information
      const defaultAdminContact = {
        FirstName: "Vineet",
        LastName: "Harbhajanka",
        ContactType: "PERSON",
        AddressLine1: "CG-84",
        City: "Kolkata",
        State: "WB",
        CountryCode: "IN",
        ZipCode: "700091",
        PhoneNumber: "+91.9007777974",
        Email: "<EMAIL>"
      }

      const adminContactInfo = AdminContact || defaultAdminContact

      const input = { // RegisterDomainRequest
        DomainName,
        DurationInYears,
        AutoRenew,
        AdminContact: { // ContactDetail
          FirstName: adminContactInfo.FirstName,
          LastName: adminContactInfo.LastName,
          ContactType: adminContactInfo.ContactType,
          AddressLine1: adminContactInfo.AddressLine1,
          City: adminContactInfo.City,
          State: adminContactInfo.State,
          CountryCode: adminContactInfo.CountryCode,
          ZipCode: adminContactInfo.ZipCode,
          PhoneNumber: adminContactInfo.PhoneNumber,
          Email: adminContactInfo.Email,
        },
        RegistrantContact: {
          FirstName: RegistrantContact.FirstName,
          LastName: RegistrantContact.LastName,
          ContactType: RegistrantContact.ContactType,
          AddressLine1: RegistrantContact.AddressLine1,
          City: RegistrantContact.City,
          State: RegistrantContact.State,
          CountryCode: RegistrantContact.CountryCode,
          ZipCode: RegistrantContact.ZipCode,
          PhoneNumber: RegistrantContact.PhoneNumber,
          Email: RegistrantContact.Email,
        },
        TechContact: {
          FirstName: adminContactInfo.FirstName,
          LastName: adminContactInfo.LastName,
          ContactType: adminContactInfo.ContactType,
          AddressLine1: adminContactInfo.AddressLine1,
          City: adminContactInfo.City,
          State: adminContactInfo.State,
          CountryCode: adminContactInfo.CountryCode,
          ZipCode: adminContactInfo.ZipCode,
          PhoneNumber: adminContactInfo.PhoneNumber,
          Email: adminContactInfo.Email,
        },
        PrivacyProtectAdminContact: true,
        PrivacyProtectRegistrantContact: true,
        PrivacyProtectTechContact: true,
      }

      const command = new RegisterDomainCommand(input)
      const response = await client.send(command)

      // Get the user from the request
      const userId = req.user.id

      // Get user
      const user = await User.findOne({ _id: userId })
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get or create platform
      let platform = null

      if (user.platforms && user.platforms.length > 0) {
        // Try to find a platform where this user is the primary user
        platform = await Platform.findOne({
          primaryUser: userId,
          _id: { $in: user.platforms }
        })
      }

      if (!platform) {
        // Make sure the user has userType set to Owner
        await User.updateOne({ _id: userId }, { userType: "Owner" })

        // Create new platform for this user
        platform = new Platform({
          primaryUser: userId,
          teamMembers: [userId] // Add the owner as a team member
        })

        // Update user with reference to the new platform
        if (!user.platforms) {
          user.platforms = []
        }
        user.platforms.push(platform._id)
        await user.save()
      }

      // Define required DNS records
      const dnsRecords = [
        {
          recordType: "A",
          host: "@",
          value: process.env.SERVER_IP_ADDRESS, // Example IP, would be replaced with actual server IP
          verified: false,
          ttl: 3600,
          purpose: "Points your domain to our server"
        },
        {
          recordType: "CNAME",
          host: "www",
          value: DomainName,
          verified: false,
          ttl: 3600,
          purpose: `Makes www.${DomainName} work`
        },
        {
          recordType: "CNAME",
          host: "fan",
          value: DomainName,
          verified: false,
          ttl: 3600,
          purpose: "Sets up the fan app subdomain"
        },
        {
          recordType: "CNAME",
          host: "creator",
          value: DomainName,
          verified: false,
          ttl: 3600,
          purpose: "Sets up the creator app subdomain"
        },
        {
          recordType: "TXT",
          host: "@",
          value: "v=spf1 include:_spf.example.com ~all", // Example SPF record
          verified: false,
          ttl: 3600,
          purpose: "Verifies domain ownership"
        }
      ]

      // Update platform with domain information
      platform.domain = {
        name: DomainName,
        provider: "AWS", // Since we're using AWS Route53
        verified: false,
        dnsRecords
      }

      // Update launch status to domain stage (3) if currently at a lower stage
      // if (platform.launchStatus < 3) {
      //   platform.launchStatus = 3
      // }

      await platform.save()

      // Update the existing payment record with registration details
      payment.metadata.registrationDetails = response
      await payment.save()

      return res.status(200).json({
        error: false,
        data: {
          domainRegistration: response,
          // payment,
          platform: {
            domain: platform.domain,
            launchStatus: platform.launchStatus
          }
        }
      })
    } catch (error) {
      console.log(`time[${moment().format("DD/MM/YYYY HH:ss")}]<http://localhost:3000/api/v1/domain/purchase (post)> ->`, error || error.message)
      return res.status(500).json({ error: true, reason: error.message })
    }
  },

  /**
   * @swagger
   * /domain/providers:
   *   get:
   *     summary: Get list of supported domain providers
   *     description: Returns a list of domain providers that users can select when connecting their domain.
   *     tags: [Domain]
   *     responses:
   *       200:
   *         description: List of domain providers
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         example: "godaddy"
   *                       name:
   *                         type: string
   *                         example: "GoDaddy"
   *                       website:
   *                         type: string
   *                         example: "https://www.godaddy.com"
   */
  async getDomainProviders(_, res) {
    try {
      return res.status(200).json({
        error: false,
        data: domainProviders
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /domain/dns-records:
   *   post:
   *     summary: Get DNS records for domain connection
   *     description: Returns the DNS records needed to connect a domain based on the provider.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - domainName
   *               - providerId
   *             properties:
   *               domainName:
   *                 type: string
   *                 description: The domain name to connect
   *                 example: "example.com"
   *               providerId:
   *                 type: string
   *                 description: The ID of the domain provider
   *                 example: "godaddy"
   *     responses:
   *       200:
   *         description: DNS records for domain connection
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     dnsRecords:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           recordType:
   *                             type: string
   *                             example: "A"
   *                           host:
   *                             type: string
   *                             example: "@"
   *                           value:
   *                             type: string
   *                             example: "***********"
   *                           ttl:
   *                             type: number
   *                             example: 3600
   *                           priority:
   *                             type: number
   *                             example: 10
   *                     providerInstructions:
   *                       type: object
   *                       properties:
   *                         title:
   *                           type: string
   *                           example: "How to add DNS records in GoDaddy"
   *                         steps:
   *                           type: array
   *                           items:
   *                             type: string
   *                             example: "Log in to your GoDaddy account"
   *       400:
   *         description: Bad request - missing or invalid input
   *       500:
   *         description: Server error
   */
  async getDnsRecords(req, res) {
    try {
      const { domainName, providerId } = req.body

      // Validate inputs
      if (!domainName) {
        return res.status(400).json({
          error: true,
          reason: "Domain name is required"
        })
      }

      if (!isValidDomain(domainName)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid domain format"
        })
      }

      if (!providerId) {
        return res.status(400).json({
          error: true,
          reason: "Provider ID is required"
        })
      }

      // Find the provider
      const provider = domainProviders.find((p) => p.id === providerId)
      if (!provider) {
        return res.status(400).json({
          error: true,
          reason: "Invalid provider ID"
        })
      }

      // Generate DNS records based on the domain name
      const dnsRecords = [
        {
          recordType: "A",
          host: "@",
          value: SERVER_IP_ADDRESS,
          ttl: 3600,
          purpose: "Points your domain to our server"
        },
        {
          recordType: "CNAME",
          host: "www",
          value: domainName,
          ttl: 3600,
          purpose: "Makes www.yourdomain.com work"
        },
        {
          recordType: "CNAME",
          host: "fan",
          value: domainName,
          ttl: 3600,
          purpose: "Sets up the fan app subdomain"
        },
        {
          recordType: "CNAME",
          host: "creator",
          value: domainName,
          ttl: 3600,
          purpose: "Sets up the creator app subdomain"
        },
        {
          recordType: "TXT",
          host: "@",
          value: `vewmee-verification=${domainName}-${Date.now().toString(36)}`,
          ttl: 3600,
          purpose: "Verifies domain ownership"
        }
      ]

      // Provider-specific instructions
      const providerInstructions = {
        title: `How to add DNS records in ${provider.name}`,
        steps: [
          `Log in to your ${provider.name} account`,
          `Navigate to the DNS management section for ${domainName}`,
          "Add each of the DNS records listed above",
          "Save your changes",
          "Wait for DNS propagation (can take up to 48 hours)"
        ]
      }

      // Add provider-specific instructions
      switch (providerId) {
        case "godaddy":
          providerInstructions.steps = [
            "Log in to your GoDaddy account",
            "Go to My Products > Domains",
            `Select ${domainName}`,
            "Click on 'DNS'",
            "Add each of the records listed above",
            "Save your changes"
          ]
          break
        case "namecheap":
          providerInstructions.steps = [
            "Log in to your Namecheap account",
            "Go to Domain List and click 'Manage' next to your domain",
            "Select the 'Advanced DNS' tab",
            "Add each of the records listed above",
            "Save your changes"
          ]
          break
        case "cloudflare":
          providerInstructions.steps = [
            "Log in to your Cloudflare account",
            `Select ${domainName} from your dashboard`,
            "Go to the DNS tab",
            "Add each of the records listed above",
            "Make sure the proxy status is set to 'DNS only' (gray cloud)"
          ]
          break
        // Add more provider-specific instructions as needed
      }

      return res.status(200).json({
        error: false,
        data: {
          dnsRecords,
          providerInstructions
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /domain/generate-urls:
   *   get:
   *     summary: Generate URLs for a verified domain
   *     description: Generates website, fan app, and creator app URLs for a verified domain.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Generated URLs
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     websiteUrl:
   *                       type: string
   *                       example: "https://example.com"
   *                     fanAppUrl:
   *                       type: string
   *                       example: "https://fan.example.com"
   *                     creatorAppUrl:
   *                       type: string
   *                       example: "https://creator.example.com"
   *       404:
   *         description: No verified domain found
   *       500:
   *         description: Server error
   */
  async generateUrls(req, res) {
    try {
      const userId = req.user.id

      // Get user
      const user = await User.findOne({ _id: userId })
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get primary platform
      if (!user.platforms || user.platforms.length === 0) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      const platform = await Platform.findOne({
        primaryUser: userId,
        _id: { $in: user.platforms }
      })

      if (!platform || !platform.domain || !platform.domain.name) {
        return res.status(404).json({
          error: true,
          reason: "Domain not found"
        })
      }

      // Check if domain is verified
      if (!platform.domain.verified) {
        return res.status(400).json({
          error: true,
          reason: "Domain is not verified yet. Please verify your domain first."
        })
      }

      const domainName = platform.domain.name

      // Generate URLs
      const websiteUrl = `https://${domainName}`
      const fanAppUrl = `https://fan.${domainName}`
      const creatorAppUrl = `https://creator.${domainName}`

      // Update platform with URLs
      platform.website = websiteUrl
      platform.fanAppLink = fanAppUrl
      platform.creatorLink = creatorAppUrl

      await platform.save()

      return res.status(200).json({
        error: false,
        data: {
          websiteUrl,
          fanAppUrl,
          creatorAppUrl
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /domain/auto/deploy:
   *   post:
   *     summary: Auto-deploy domain with AWS services
   *     description: Automatically deploys a domain using AWS services (Route53, ACM, CloudFront). Creates SSL certificates and CloudFront distribution for the domain. The S3 bucket is automatically selected based on the deploymentType.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - domain
   *               - subDomain
   *               - comment
   *               - deploymentType
   *             properties:
   *               domain:
   *                 type: string
   *                 description: The main domain name.
   *                 example: "example.com"
   *               subDomain:
   *                 type: string
   *                 description: The subdomain to deploy.
   *                 example: "app.example.com"
   *               comment:
   *                 type: string
   *                 description: Comment for the deployment.
   *                 example: "Website deployment"
   *               deploymentType:
   *                 type: string
   *                 description: Type of deployment which determines the S3 bucket to use. Must be one of 'creator', 'fan', or 'website'.
   *                 enum: [creator, fan, website]
   *                 example: "creator"
   *               callerReference:
   *                 type: string
   *                 description: Optional unique identifier for the CloudFront distribution (used for idempotency). If not provided, a unique value will be generated.
   *                 example: "deploy-example.com-1621022400000"
   *     responses:
   *       200:
   *         description: Deployment initiated or completed.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     hostedZoneId:
   *                       type: string
   *                       example: "Z1PA6795UKMFR9"
   *                     message:
   *                       type: string
   *                       example: "Deployment in progress"
   *                     certificateDetails:
   *                       type: object
   *                       properties:
   *                         certificateName:
   *                           type: string
   *                           example: "_abc123.example.com."
   *                         certificateValue:
   *                           type: string
   *                           example: "abc123.acm-validations.aws."
   *                         certificateType:
   *                           type: string
   *                           example: "CNAME"
   *                         certificateArn:
   *                           type: string
   *                           example: "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
   *                     cloudfrontDetails:
   *                       type: object
   *                       properties:
   *                         name:
   *                           type: string
   *                           example: "app.example.com"
   *                         recordType:
   *                           type: string
   *                           example: "CNAME"
   *                         value:
   *                           type: string
   *                           example: "d123.cloudfront.net"
   *                     domain:
   *                       type: string
   *                       example: "example.com"
   *                     subDomain:
   *                       type: string
   *                       example: "app.example.com"
   *       400:
   *         description: Bad request - missing or invalid input.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "domain, subDomain, comment, deploymentType are required"
   *       500:
   *         description: Server error.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Error creating certificate or CloudFront distribution"
   */
  async autoDeploy(req, res) {
    try {
      const {
        domain, subDomain, comment, deploymentType
      } = req.body

      const idempotencyToken = Date.now().toString(36) + Math.random().toString(36).substring(2)
      const callerReference = `deploy-${domain}-${Date.now()}`

      let s3BucketName = ""
      if (deploymentType.toLowerCase() === "creator") s3BucketName = process.env.AWS_CREATOR_APP_BUCKET
      else if (deploymentType.toLowerCase() === "fan") s3BucketName = process.env.AWS_FAN_APP_BUCKET
      else if (deploymentType.toLowerCase() === "website") s3BucketName = process.env.AWS_WEBSITE_BUCKET
      else {
        return res.status(400).json({ error: true, message: "deploymentType must be creator, fan, or website" })
      }
      // const s3BucketName = process.env.AWS_CREATOR_APP_BUCKET

      if (!domain || !subDomain || !comment || !deploymentType) {
        return res.status(400).json({ error: true, message: "domain, subDomain, comment, deploymentType are required" })
      }
      if (!isValidDomain(domain) || !isValidDomain(subDomain)) {
        return res.status(400).json({ error: true, message: "domain or subDomain is not valid" })
      }

      const hostedZoneCommand = new ListHostedZonesCommand({})
      const hostedZoneResponse = await clientRoute53.send(hostedZoneCommand)
      let hostedZoneId = ""
      let certificateDetails = {}
      let cloudfrontDetails = {}
      hostedZoneResponse.HostedZones.forEach((element) => {
        if (element.Name === `${domain}.`) {
          hostedZoneId = element.Id.split("/").at(-1)
        }
      })
      let listCertificatesResponse
      let listResourceRecordSetsResponse
      let findCertificateRecord
      if (hostedZoneId !== "") {
        const listResourceRecordSetsCommand = new ListResourceRecordSetsCommand({
          HostedZoneId: hostedZoneId
        })
        listResourceRecordSetsResponse = await clientRoute53.send(listResourceRecordSetsCommand)
        const listCertificatesCommand = new ListCertificatesCommand({})

        listCertificatesResponse = await clientACM.send(listCertificatesCommand)

        findCertificateRecord = listCertificatesResponse.CertificateSummaryList.find((element) => (element.DomainName === domain || element.DomainName === `*.${subDomain}`))

        const findFailedCertificate = findCertificateRecord && (findCertificateRecord.Status === "FAILED" || findCertificateRecord.Status === "EXPIRED")
        if (findFailedCertificate !== undefined) {
          const deleteCertificateCommand = new DeleteCertificateCommand({
            CertificateArn: findFailedCertificate.CertificateArn
          })
          await clientACM.send(deleteCertificateCommand)
        }
      }

      const params = {}
      if (findCertificateRecord && (findCertificateRecord.Status === "ISSUED" || findCertificateRecord.Status === "PENDING_VALIDATION")) {
        params.CertificateArn = findCertificateRecord.CertificateArn
      } else {
        const certificateInput = {
          DomainName: subDomain || `*.${domain}`,
          ValidationMethod: "DNS",
          IdempotencyToken: idempotencyToken,
          DomainValidationOptions: [
            {
              DomainName: subDomain || domain,
              ValidationDomain: domain
            },
          ],
          Options: {
            CertificateTransparencyLoggingPreference: "ENABLED",
          },
          KeyAlgorithm: "RSA_2048"
        }
        const certificateCommand = new RequestCertificateCommand(certificateInput)
        const certificateResponse = await clientACM.send(certificateCommand)
        const certificateArn = certificateResponse.CertificateArn
        params.CertificateArn = certificateArn
      }

      const getCertificateDetails = () => new Promise((resolve, reject) => {
        setTimeout(async () => {
          try {
            const describeCertificateCommand = new DescribeCertificateCommand(params)
            const describeCertificateResponse = await clientACM.send(describeCertificateCommand)

            const certificateDomainValidationOptions = describeCertificateResponse.Certificate.DomainValidationOptions.find(
              (element) => element.DomainName === subDomain || element.DomainName === domain
            )

            if (!certificateDomainValidationOptions) {
              return reject(new Error("No matching certificate found"))
            }

            const certificateName = certificateDomainValidationOptions.ResourceRecord.Name
            const certificateValue = certificateDomainValidationOptions.ResourceRecord.Value
            const certificateType = certificateDomainValidationOptions.ResourceRecord.Type
            const certificateArn = describeCertificateResponse.Certificate.CertificateArn

            if (hostedZoneId !== "") {
              // Check if this hosted zone already added the certificate values
              const findCertificateRecordItem = listResourceRecordSetsResponse.ResourceRecordSets.find(
                (element) => element.Name === certificateName && element.Type === certificateType
              )

              if (findCertificateRecordItem !== undefined) {
                return resolve({ certificateName, certificateValue, certificateType })
              }

              const createcertificateRecordParams = {
                ChangeBatch: {
                  Changes: [
                    {
                      Action: "CREATE",
                      ResourceRecordSet: {
                        Name: certificateName,
                        Type: certificateType,
                        TTL: 300,
                        ResourceRecords: [{ Value: certificateValue }],
                      },
                    },
                  ],
                  Comment: "",
                },
                HostedZoneId: hostedZoneId,
              }

              const isCertificatePresent = listResourceRecordSetsResponse.ResourceRecordSets.find(
                (element) => element.Name === certificateName && element.Type === certificateType && element.ResourceRecords[0].Value === certificateValue
              )
              if (isCertificatePresent) {
                return resolve({ certificateName, certificateValue, certificateType })
              }
              const createcertificateRecordCommand = new ChangeResourceRecordSetsCommand(createcertificateRecordParams)
              await clientRoute53.send(createcertificateRecordCommand)
            }

            // Save certificate information to the platform
            try {
              // Extract the base domain from the subdomain
              const domainParts = subDomain ? subDomain.split(".") : domain.split(".")
              const baseDomain = domainParts.length >= 2
                ? `${domainParts[domainParts.length - 2]}.${domainParts[domainParts.length - 1]}`
                : (subDomain || domain)

              // Find the platform with this domain
              const platform = await Platform.findOne({ "domain.name": baseDomain })

              if (platform) {
                // Update the platform with certificate information
                if (!platform.domain.certificates) {
                  platform.domain.certificates = []
                }

                // Check if this certificate already exists
                const existingCertIndex = platform.domain.certificates.findIndex(
                  (cert) => cert.certificateArn === certificateArn
                )

                if (existingCertIndex >= 0) {
                  // Update existing certificate
                  platform.domain.certificates[existingCertIndex] = {
                    certificateArn,
                    subDomain: subDomain || domain,
                    issuedAt: new Date()
                  }
                } else {
                  // Add new certificate
                  platform.domain.certificates.push({
                    certificateArn,
                    subDomain: subDomain || domain,
                    issuedAt: new Date()
                  })
                }

                await platform.save()
                console.log(`Saved certificate for ${subDomain || domain} with ARN ${certificateArn} to platform ${platform._id}`)
              }
            } catch (certSaveError) {
              console.error(`Error saving certificate information: ${certSaveError.message}`)
            }

            resolve({
              certificateName, certificateValue, certificateType, certificateArn
            })
          } catch (error) {
            reject(error)
          }
        }, 7000)
      })

      if (hostedZoneId === "") {
        certificateDetails = await getCertificateDetails()
        return res.status(200).json({
          error: false,
          data: {
            hostedZoneId, message: "Please add the certificate first in your dns name server", certificateDetails, deploymentType
          }
        })
      }
      getCertificateDetails()

      const interval = setInterval(async () => {
        const describeCertificateCommand = new DescribeCertificateCommand(params)
        const describeCertificateResponse = await clientACM.send(describeCertificateCommand)
        const certificateStatus = describeCertificateResponse.Certificate.Status
        const certificateArn = describeCertificateResponse.Certificate.CertificateArn

        console.log("certificateStatus", certificateStatus)

        if (certificateStatus === "ISSUED") {
          clearInterval(interval)
          const command = new ListDistributionsCommand({})
          const response = await clientCloudFront.send(command)

          const distributions = (response.DistributionList && response.DistributionList.Items) || []

          for (const distribution of distributions) {
            const aliases = (distribution.Aliases && distribution.Aliases.Items) || []
            if (aliases.includes(subDomain)) {
              return
            }
          }
          const createCloudFrontParam = {
            DistributionConfig: {
              CallerReference: callerReference,
              Aliases: {
                Quantity: 1,
                Items: [
                  subDomain
                ]
              },
              Comment: comment,
              DefaultRootObject: "index.html",
              Origins: {
                Quantity: 1,
                Items: [
                  {
                    Id: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`, // "vewmee-creator.s3.us-east-1.amazonaws.com",
                    DomainName: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`,
                    S3OriginConfig: {
                      OriginAccessIdentity: "" // if public bucket
                    }
                  }
                ]
              },
              DefaultCacheBehavior: {
                TargetOriginId: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`,
                ViewerProtocolPolicy: "redirect-to-https",

                CachePolicyId: "658327ea-f89d-4fab-a63d-7e88639e58f6",
                Compress: true,
              },
              CustomErrorResponses: {
                Quantity: 2,
                Items: [
                  {
                    ErrorCode: 403,
                    ResponsePagePath: "/index.html",
                    ResponseCode: "200",
                    ErrorCachingMinTTL: 300,
                  },
                  {
                    ErrorCode: 404,
                    ResponsePagePath: "/index.html",
                    ResponseCode: "200",
                    ErrorCachingMinTTL: 300,
                  }
                ],
              },

              Enabled: true,
              ViewerCertificate: {
                ACMCertificateArn: certificateArn,
                SSLSupportMethod: "sni-only",
                MinimumProtocolVersion: "TLSv1.2_2021",
                CertificateSource: "acm"
              },
              IsIPV6Enabled: true
            }
          }

          console.log(createCloudFrontParam)
          const createCloudFrontCommand = new CreateDistributionCommand(createCloudFrontParam)
          const cloudFrontResponse = await clientCloudFront.send(createCloudFrontCommand)
          const { DomainName } = cloudFrontResponse.Distribution
          cloudfrontDetails = {
            name: subDomain || domain,
            recordType: "CNAME",
            value: DomainName
          }
          if (hostedZoneId !== "") {
            const createRecordDistributionParams = {
              ChangeBatch: { // required
                Changes: [ // required
                  {
                    Action: "CREATE",
                    ResourceRecordSet: {
                      Name: subDomain || domain,
                      Type: "CNAME",
                      TTL: 300,
                      ResourceRecords: [
                        {
                          Value: DomainName
                        }
                      ]
                    }
                  },
                ],
                Comment: "",
              },
              HostedZoneId: hostedZoneId
            }
            // if hosted zone already have cloudfront record, dont add this again
            const findCLoudFrontRecord = listResourceRecordSetsResponse.ResourceRecordSets.find((element) => element.Name === (subDomain || domain) && element.Type === "CNAME")
            if (findCLoudFrontRecord !== undefined) {
              return
            }
            const createRecordCloudfrontCommand = new ChangeResourceRecordSetsCommand(createRecordDistributionParams)
            await clientRoute53.send(createRecordCloudfrontCommand)
          }
        }
      }, 1000)
      const message = hostedZoneId === "" ? "Please add certificate details and cloudfront details to your name server" : "Deployment in progress"
      return res.status(200).json({
        error: false,
        data: {
          hostedZoneId, message, certificateDetails, cloudfrontDetails, domain, subDomain, deploymentType
        }
      })
    } catch (error) {
      console.log(`time[${moment().format("DD/MM/YYYY HH:ss")}]<http://localhost:3000/api/v1/domain/auto/deploy (post)> ->`, error || error.message)
      return res.status(500).json({ error: true, reason: error.message })
    }
  },

  /**
   * @swagger
   * /domain/cloudfront:
   *   post:
   *     summary: Create CloudFront distribution
   *     description: Creates a CloudFront distribution for a domain/subdomain pointing to an S3 bucket with SSL certificate. This endpoint is used when you already have a valid SSL certificate and want to create a CloudFront distribution.
   *     tags: [Domain]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - domain
   *               - subDomain
   *               - deploymentType
   *               - certificateArn
   *             properties:
   *               domain:
   *                 type: string
   *                 description: The main domain name
   *                 example: "example.com"
   *               subDomain:
   *                 type: string
   *                 description: The subdomain to create the CloudFront distribution for
   *                 example: "app.example.com"
   *               deploymentType:
   *                 type: string
   *                 description: Type of deployment which determines the S3 bucket to use. Must be one of 'creator', 'fan', or 'website'.
   *                 enum: [creator, fan, website]
   *                 example: "creator"
   *               certificateArn:
   *                 type: string
   *                 description: The ARN of the SSL certificate to use for the distribution
   *                 example: "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
   *               comment:
   *                 type: string
   *                 description: Optional comment for the CloudFront distribution
   *                 example: "Website distribution"
   *               callerReference:
   *                 type: string
   *                 description: Optional unique identifier for the CloudFront distribution (used for idempotency). If not provided, a unique value will be generated.
   *                 example: "distribution-2023-05-15"
   *     responses:
   *       200:
   *         description: CloudFront distribution created or existing distribution found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     cloudfrontDetails:
   *                       type: object
   *                       properties:
   *                         name:
   *                           type: string
   *                           example: "app.example.com"
   *                         type:
   *                           type: string
   *                           example: "CNAME"
   *                         value:
   *                           type: string
   *                           example: "d123456abcdef8.cloudfront.net"
   *                     domain:
   *                       type: string
   *                       example: "example.com"
   *                     subDomain:
   *                       type: string
   *                       example: "app.example.com"
   *       400:
   *         description: Bad request - missing required parameters
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "Domain, Subdomain, certificate and bucketname are required"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Error creating CloudFront distribution"
   */
  async cloudfront(req, res) {
    try {
      const {
        domain, subDomain, comment = "", callerReference = "", certificateArn, deploymentType
      } = req.body

      // Generate a default callerReference if not provided
      const uniqueCallerReference = callerReference || `deploy-${domain}-${subDomain}-${Date.now()}`

      let s3BucketName = ""
      if (deploymentType.toLowerCase() === "creator") s3BucketName = process.env.AWS_CREATOR_APP_BUCKET
      else if (deploymentType.toLowerCase() === "fan") s3BucketName = process.env.AWS_FAN_APP_BUCKET
      else if (deploymentType.toLowerCase() === "website") s3BucketName = process.env.AWS_WEBSITE_BUCKET
      else {
        return res.status(400).json({ error: true, message: "deploymentType must be creator, fan, or website" })
      }
      if (domain === undefined || subDomain === undefined || certificateArn === undefined) {
        return res.status(400).json({ error: true, message: "Domain, Subdomain, certificate and deploymentType are required" })
      }
      const command = new ListDistributionsCommand({})
      const response = await clientCloudFront.send(command)

      const distributions = (response.DistributionList && response.DistributionList.Items) || []

      for (const distribution of distributions) {
        const aliases = (distribution.Aliases && distribution.Aliases.Items) || []
        if (aliases.includes(subDomain)) {
          return res.status(200).json({ error: false, data: distribution })
        }
      }
      const createCloudFrontParam = {
        DistributionConfig: {
          CallerReference: uniqueCallerReference,
          Aliases: {
            Quantity: 1,
            Items: [
              subDomain
            ]
          },
          Comment: comment,
          DefaultRootObject: "index.html",
          Origins: {
            Quantity: 1,
            Items: [
              {
                Id: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`, // "vewmee-creator.s3.us-east-1.amazonaws.com",
                DomainName: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`,
                S3OriginConfig: {
                  OriginAccessIdentity: "" // if public bucket
                }
              }
            ]
          },
          DefaultCacheBehavior: {
            TargetOriginId: `${s3BucketName}.s3.${process.env.AWS_BUCKET_REGION}.amazonaws.com`,
            ViewerProtocolPolicy: "redirect-to-https",

            CachePolicyId: "658327ea-f89d-4fab-a63d-7e88639e58f6",
            Compress: true,
          },
          CustomErrorResponses: {
            Quantity: 2,
            Items: [
              {
                ErrorCode: 403,
                ResponsePagePath: "/index.html",
                ResponseCode: "200",
                ErrorCachingMinTTL: 300,
              },
              {
                ErrorCode: 404,
                ResponsePagePath: "/index.html",
                ResponseCode: "200",
                ErrorCachingMinTTL: 300,
              }
            ],
          },

          Enabled: true,
          ViewerCertificate: {
            ACMCertificateArn: certificateArn,
            SSLSupportMethod: "sni-only",
            MinimumProtocolVersion: "TLSv1.2_2021",
            CertificateSource: "acm"
          },
          IsIPV6Enabled: true
        }
      }

      const createCloudFrontCommand = new CreateDistributionCommand(createCloudFrontParam)
      const cloudFrontResponse = await clientCloudFront.send(createCloudFrontCommand)
      console.log("cloudFrontResponse ===================", cloudFrontResponse)
      const { DomainName } = cloudFrontResponse.Distribution
      const cloudfrontDetails = {
        name: subDomain || domain,
        type: "CNAME",
        value: DomainName
      }
      // Save certificate information to the platform
      try {
        // Extract the base domain from the subdomain
        const domainParts = domain.split(".")
        const baseDomain = domainParts.length >= 2
          ? `${domainParts[domainParts.length - 2]}.${domainParts[domainParts.length - 1]}`
          : domain

        // Find the platform with this domain
        const platform = await Platform.findOne({ "domain.name": baseDomain })

        if (platform) {
          // Update the platform with certificate information
          if (!platform.domain.certificates) {
            platform.domain.certificates = []
          }

          // Check if this certificate already exists
          const existingCertIndex = platform.domain.certificates.findIndex(
            (cert) => cert.certificateArn === certificateArn
          )

          if (existingCertIndex >= 0) {
            // Update existing certificate
            platform.domain.certificates[existingCertIndex] = {
              certificateArn,
              subDomain,
              issuedAt: new Date()
            }
          } else {
            // Add new certificate
            platform.domain.certificates.push({
              certificateArn,
              subDomain,
              issuedAt: new Date()
            })
          }

          await platform.save()
          console.log(`Saved certificate for ${subDomain} with ARN ${certificateArn} to platform ${platform._id}`)
        }
      } catch (certSaveError) {
        console.error(`Error saving certificate information: ${certSaveError.message}`)
      }

      return res.status(200).json({
        error: false,
        data: {
          cloudfrontDetails, domain, subDomain, deploymentType
        }
      })
    } catch (error) {
      console.log("error while creating cloudfront", error)
      return res.status(200).json({
        error: true,
        reason: error.message,
      })
    }
  },

  async webhookStatus(req, res) {
    try {
      const payload = req.body

      const emailData = JSON.parse(payload)

      console.log("emailData", emailData)

      //       {
      //   version: '0',
      //   id: 'f2c59aa7-ecdf-d6f2-b129-f0c5ef6469e8',
      //   'detail-type': 'ACM Certificate Available',
      //   source: 'aws.acm',
      //   account: '************',
      //   time: '2025-05-19T08:47:12Z',
      //   region: 'us-east-1',
      //   resources: [
      //     'arn:aws:acm:us-east-1:************:certificate/88d4d177-29c2-4f79-b78e-707792e19d98'
      //   ],
      //   detail: {
      //     CertificateType: 'AMAZON_ISSUED',
      //     CommonName: 'fan.viewme.click',
      //     DomainValidationMethod: 'DNS',
      //     Action: 'ISSUANCE',
      //     DaysToExpiry: 394,
      //     CertificateCreatedDate: '2025-05-19T08:46:53.000Z',
      //     CertificateExpirationDate: '2026-06-17T23:59:59.000Z',
      //     InUse: true,
      //     Exported: false
      //   }
      // }
      if (emailData.Type === "SubscriptionConfirmation") {
        const { SubscribeURL } = emailData
        await axios.get(SubscribeURL)
        return res.status(200).send("Subscription confirmed")
      }

      if (emailData.detail.CertificateType === "AMAZON_ISSUED") {
        const certificateArn = emailData.resources[0]
        const subDomain = emailData.detail.CommonName

        try {
          // Extract the base domain from the subdomain
          const domainParts = subDomain.split(".")
          const baseDomain = domainParts.length >= 2
            ? `${domainParts[domainParts.length - 2]}.${domainParts[domainParts.length - 1]}`
            : subDomain

          // Find the platform with this domain
          const platform = await Platform.findOne({ "domain.name": baseDomain })

          if (platform) {
            // Update the platform with certificate information
            if (!platform.domain.certificates) {
              platform.domain.certificates = []
            }

            // Check if this certificate already exists
            const existingCertIndex = platform.domain.certificates.findIndex(
              (cert) => cert.certificateArn === certificateArn
            )

            if (existingCertIndex >= 0) {
              // Update existing certificate
              platform.domain.certificates[existingCertIndex] = {
                certificateArn,
                subDomain,
                issuedAt: new Date(),
                expiresAt: emailData.detail.CertificateExpirationDate
                  ? new Date(emailData.detail.CertificateExpirationDate)
                  : null
              }
            } else {
              // Add new certificate
              platform.domain.certificates.push({
                certificateArn,
                subDomain,
                issuedAt: new Date(),
                expiresAt: emailData.detail.CertificateExpirationDate
                  ? new Date(emailData.detail.CertificateExpirationDate)
                  : null
              })
            }

            await platform.save()
            console.log(`Saved certificate for ${subDomain} with ARN ${certificateArn} to platform ${platform._id}`)
          } else {
            console.log(`No platform found with domain ${baseDomain} for certificate ${certificateArn}`)
          }
        } catch (error) {
          console.error(`Error saving certificate information: ${error.message}`)
        }
      }

      // lets send one notification letting know that the story is ready with storyId
      return res.status(200).json({
        error: false,
      })
    } catch (error) {
      return res.status(200).json({
        error: true,
        reason: error.message,
      })
    }
  }
}
