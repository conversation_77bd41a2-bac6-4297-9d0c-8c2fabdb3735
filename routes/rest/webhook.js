/* eslint-disable max-len */
/* eslint-disable default-case */
const {
  User, Subscription, Payment, Platform
} = require("../../models/index")
const agenda = require("../../agenda")
const mail = require("../../lib/mail")
const stripeService = require("../../lib/stripe")

const handleInvoicePaymentSucceeded = async (intentId, paymentMethod) => {
  try {
    // Schedule the agenda job to process payment
    console.log("inside handleInvoicePaymentSucceeded")
    const paymentIntentDocument = await Payment.findOne({
      paymentIntentId: intentId,
    }).populate("_user", "stripeId platforms").lean()

    if (paymentIntentDocument === null) {
      console.error("paymentIntentDocument not found:", intentId)
      throw new Error("Payment intent document not found")
    }
    // Create subscription with 3-month trial
    console.log("paymentIntentDocument", paymentIntentDocument)
    if (paymentIntentDocument.paymentFor === "subscription-setup") {
      const now = new Date()
      const threeMonthsLater = new Date(now)
      threeMonthsLater.setMonth(now.getMonth() + 3)

      const userId = paymentIntentDocument._user._id
      const platformId = paymentIntentDocument._platform
      const subscription = await Subscription.create({
        user: paymentIntentDocument._user,
        status: "active",
        setupFeePaid: true,
        startDate: now,
        endDate: threeMonthsLater,
        currentPeriodStart: now,
        currentPeriodEnd: threeMonthsLater,
        stripeId: paymentIntentDocument._user.stripeId,
        model: "setup-fee", // Default model
        defaultPaymentMethodId: paymentIntentDocument.paymentMethod,
        _platform: platformId
      })

      // Calculate date 4 months from now
      const fourMonthsLater = new Date()
      fourMonthsLater.setMonth(fourMonthsLater.getMonth() + 4)

      await agenda.schedule(fourMonthsLater, "process-successful-payment", { userId, subscriptionId: subscription._id })
      console.log(`Scheduled payment processing job for intent: ${intentId} to run on ${fourMonthsLater.toISOString()}`)

      // Update payment intent with completed status and link to subscription
      await Payment.updateOne(
        { _id: paymentIntentDocument._id },
        {
          $set: {
            status: "completed",
            _subscription: subscription._id,
            paymentMethod
          }
        }
      )

      // Update platform launch status to 3 for subscription setup
      // await Platform.updateOne(
      //   { _id: platformId },
      //   { $set: { launchStatus: 3 } }
      // )
      console.log(`Updated platform ${platformId} launch status to 3 after subscription setup payment`)

      // Get user details for email
      const user = await User.findOne({ _id: userId }).lean()

      if (user) {
        // Send subscription confirmation email with error handling
        try {
          mail("subscription-confirmation", {
            to: user.email,
            subject: "You're In! Subscription Confirmed 🎉",
            locals: {
              firstName: user.name ? user.name.first : "User",
              url: `${process.env.SITE_URL}/login`,
              platformName: "Vewmee White Level"
            }
          })
          console.log(`Subscription confirmation email sent to ${user.email}`)
        } catch (emailError) {
          console.error(`Error sending subscription confirmation email to ${user.email}:`, emailError)
          // Continue execution even if email fails
        }
      }
    } else if (paymentIntentDocument.paymentFor === "domain") {
      // Update payment intent with completed status
      await Payment.updateOne(
        { _id: paymentIntentDocument._id },
        {
          $set: {
            status: "completed",
            paymentMethod
          }
        }
      )
    }

    // Update user
    // await User.updateOne({ _id: paymentIntentDocument._user._id }, { $set: { subscription: subscription._id, launchStatus: "subscribed" } })
  } catch (error) {
    console.error("Error handling handleInvoicePaymentSucceeded:", error)
  }
}

const handlePaymentFailed = async (intentId) => {
  try {
    // Update payment intent
    await Payment.updateOne({ _id: intentId }, { $set: { status: "failed" } })
  } catch (error) {
    console.error("Error handling invoice.payment_failed:", error)
  }
}

const handlePaymentCancelled = async (intentId) => {
  try {
    // Update payment intent
    await Payment.updateOne({ _id: intentId }, { $set: { status: "canceled" } })
  } catch (error) {
    console.error("Error handling account.updated:", error)
  }
}
module.exports = {
  /**
   * @swagger
   * /webhook/stripe:
   *   post:
   *     summary: Stripe webhook handler
   *     description: Handles Stripe webhook events
   *     tags: [Webhook]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Webhook received and processed
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 received:
   *                   type: boolean
   *                   example: true
   *       400:
   *         description: Bad request or webhook processing error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Missing Stripe signature"
   */
  async stripe(req, res) {
    const payload = req.body
    console.log("Stripe webhook called, payload-->", payload)
    console.log("Stripe webhook called, payload-->", payload.data.object.payment_method)

    const intentId = payload.data.object.id

    try {
      // Handle the event based on its type
      if (payload.type === "payment_intent.succeeded") {
        // console.log("Payment intent succeeded =============", intentId)
        await handleInvoicePaymentSucceeded(intentId, payload.data.object.payment_method)
      } else if (payload.type === "payment_intent.payment_failed") {
        await handlePaymentFailed(intentId)
      } else if (payload.type === "payment_intent.canceled") {
        await handlePaymentCancelled(intentId)
      }
      // Add more event types as needed

      // Return a 200 response to acknowledge receipt of the event
      return res.json({ received: true })
    } catch (error) {
      console.error("Stripe webhook error:", error)
      return res.status(400).json({ error: true, reason: error.message })
    }
  },

  async updateStripeAccount(req, res) {
    const payload = req.body
    console.log(
      "updateStripeAccount payload ====",
      payload
    )
    try {
      if (payload.type === "account.updated") {
        const { data } = payload
        const account = data.object
        const user = await User.findOne({ vendorStripeId: account.id }).exec()
        if (user === null) {
          return res.status(200).json({
            error: true,
            reason: "USER_NOT_FOUND"
          })
        }

        if (account.individual !== undefined) {
          const { address } = account.individual
          console.log("address", address)
          // user.address.city = address.city
          // user.address.street = address.line1
          // user.address.zip = address.postal_code
          // user.address.state = address.state
          // user.address.country = address.country
        }

        user.paymentEnabled = account.charges_enabled
        user.payoutEnabled = account.payouts_enabled

        const resp = await stripeService.retrieveVendorAccount(account.id)
        const needToCheckData = {
          transfers: resp.capabilities.transfers,
          payouts_enabled: resp.payouts_enabled,
          charges_enabled: resp.charges_enabled,
          future_requirements: resp.future_requirements
        }
        console.log("needToCheckData", needToCheckData)
        if (needToCheckData.transfers === "active" && needToCheckData.payouts_enabled === true && needToCheckData.charges_enabled === true && needToCheckData.future_requirements.alternatives.length === 0 && needToCheckData.future_requirements.currently_due.length === 0 && needToCheckData.future_requirements.eventually_due.length === 0 && needToCheckData.future_requirements.past_due.length === 0 && needToCheckData.future_requirements.pending_verification.length === 0) {
          user.stripeKycRequire = false
        }
      }
      return res.json({
        error: false,
      })
    } catch (error) {
      return res.status(200).json({
        error: true,
        reason: error.message,
      })
    }
  },
}
