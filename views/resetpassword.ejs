<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Vewmee Reset Password</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <link
      rel="icon"
      href="/docs/assets/favicon.ico"
      type="image/x-icon"
    />
    <link
      rel="icon"
      href="/docs/assets/favicon-32x32.png"
      sizes="32x32"
    />

    <style>
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
    </style>
    <script src="/docs/vendor/jquery.min.js"></script>

    <style>
      * {
        box-sizing: border-box;
      }

      .formError {
        color: #f16667;
        font-size: 12px;
        margin-top: 2px;
      }

      body {
        margin: 0;
        padding: 0;
        background-color: #f3f7fe;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: "Poppins", sans-serif;
      }

      /* login  wrapper*/
      .resetWrapper {
        max-width: 550px;
        width: 100%;
      }

      .projectLogo {
        height: 60px;
        margin: 20px auto;
        display: block;
      }

      .biglogo {
        position: absolute;
        right: -10px;
        bottom: 20px;
        width: 300px;
        opacity: 0.7;
        z-index: -1;
      }

      .resetForm {
        overflow: hidden;
        position: relative;
        padding: 30px;
        background-color: white;
        border-radius: 8px;
        z-index: 1;
      }

      .resetForm h2 {
        font-size: 24px;
        color: #1b2430;
        margin-top: 0;
        margin-bottom: 25px;
        text-align: center;
      }

      /*  floatingLabel */
      .floatingLabel {
        position: relative;
      }

      .formGroup {
        margin-bottom: 20px;
        width: 100%;
      }

      .form-control {
        height: 48px;
        width: 100%;
        border: 1px solid #dddddd;
        border-radius: 8px !important;
        color: #1b2430 !important;
        font-weight: 600;
        font-size: 14px;
        z-index: unset !important;
        padding-bottom: 0 !important;
        padding-top: 16px !important;
        padding-left: 12px !important;
        background-color: transparent;
      }

      .formGroup .form-control:focus {
        border-color: #222 !important;
        outline: none;
      }

      .floatingLabel label {
        position: absolute;
        top: 14px;
        left: 13px;
        transition: all 0.3s;
        margin-bottom: 0;
        pointer-events: none;
        font-weight: 400;
        font-size: 14px;
        color: #767676;
      }

      .floatingLabel .form-control:focus + label {
        top: 5px;
        font-size: 12px;
        transition: all 0.3s;
      }

      .floatingLabel .form-control:not(:placeholder-shown) + label {
        top: 5px;
        font-size: 12px !important;
        transition: all 0.3s;
      }

      .btnSubmit {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        font-size: 16px;
        margin: 10px 0;
        font-weight: 600;
        background-color: #f7374f;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #f7374f !important;
        color: white;
        cursor: pointer;
      }

      .loginFooter {
        color: #828282;
        width: 100%;
        text-align: center;
        margin: 10px auto;
        font-size: 13px;
        font-weight: 400;
      }

      .poweredBy a {
        color: #f7374f;
        text-decoration: none !important;
        display: inline-block;
        margin-left: 2px;
        font-size: 13px;
        font-weight: 400;
      }

      .alertMessage {
        border-radius: 8px;
        text-align: center;
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99;
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0;
        visibility: hidden;
        transition: 0.5s ease-in;
      }

      .alertMessage.show {
        visibility: visible;
        opacity: 1;
      }

      .msgBox {
        background-color: white;
        width: 500px;
        height: auto;
        padding: 40px 20px;
        border-radius: 4px;
      }

      .close {
        width: 150px;
        height: 44px;
        margin-top: 30px;
        background-color: #f7374f;
        border: 1px solid #f7374f;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
      }

      .alertMessage h1 {
        font-size: 14px;
        margin: 0px 0 15px !important;
        font-weight: 500;
      }

      .alertMessage p {
        font-size: 14px;
        margin: 0;
      }
    </style>
  </head>

  <body>
    <div class="resetWrapper">
      <img src="/docs/img/logo.png" alt="Vewmee Logo" class="projectLogo" />

      <div class="resetForm">
        <h2 class="title">Recover Your Password</h2>

        <!-- Success Message Modal -->
        <div class="alertMessage" id="success-msg">
          <div class="msgBox">
            <div style="width: 60px; height: 60px; background-color: #4CAF50; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px; font-weight: bold;">✓</div>
            <h1>Your password Successfully Reset.</h1>
            <p>Please Login</p>
            <button class="close" id="login-btn">Login</button>
          </div>
        </div>

        <!-- Error Message Modal -->
        <div class="alertMessage" id="error-msg">
          <div class="msgBox">
            <div style="width: 60px; height: 60px; background-color: #f44336; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px; font-weight: bold;">!</div>
            <h1>Failed to Change password for <%= handle %></h1>
            <p>Reason: <span id="error-reason"></span></p>
            <button class="close" id="close-error-btn">Close</button>
          </div>
        </div>

        <form name="changepass-form" id="changepass-form">
          <input type="hidden" name="token" id="token" value="<%= token %>" />

          <div class="formGroup floatingLabel">
            <input
              type="text"
              class="form-control"
              disabled
              id="handle"
              placeholder=" "
              value="<%= handle %>"
            />
            <label>Email</label>
          </div>

          <div class="formGroup floatingLabel">
            <input
              type="password"
              class="form-control"
              name="newpass"
              id="newpass"
              placeholder=" "
              required
            />
            <label>New Password</label>
            <span class="formError" id="newpass-error" style="display: none;"></span>
          </div>

          <div class="formGroup floatingLabel">
            <input
              type="password"
              class="form-control"
              name="newpass-verify"
              id="newpass-verify"
              placeholder=" "
              required
            />
            <label>Confirm New Password</label>
            <span class="formError" id="confirm-error" style="display: none;"></span>
          </div>

          <input
            type="submit"
            class="btnSubmit"
            name="submit"
            id="submit"
            value="Reset Password"
          />
        </form>
      </div>
      <div class="loginFooter">
        <span>© 2025 Vewmee</span>
      </div>
    </div>

    <script src="/js/resetpassword.js"></script>
  </body>
</html>