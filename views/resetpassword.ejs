<!-- <!DOCTYPE html>
<html lang=en>

<head>
  <title>Reset Password</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no">
  <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css" rel="stylesheet">
  <link href='https://fonts.googleapis.com/css?family=Roboto:400,500italic' rel='stylesheet' type='text/css'>
  <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
  <link href="/stylesheets/new-password.css" rel="stylesheet">
</head>

<body>
  <div class="create-new-password-bg">

    <div id="success-msg" style='display: none; text-align: center;'>
      <h1>Successfully Changed password for <%= handle %></h1>
      You can now use the new password to Login
    </div>
    <div id="error-msg" style='display: none; text-align: center;'>
      <h1>Failed to Change password for <%= handle %></h1>
      Reason: <span id='error-reason'></span>
    </div>
    <form name="changepass-form" id="changepass-form">
      <h1>Change Password Form</h1>
      <div class="alert alert-danger" id="alert" style="display: none;">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        <p>Passwords Must Match!</p>
      </div>
      <input type="hidden" name="token" id="token" placeholder="Password" value="<%= token %>" />
      <input type="text" readonly id="handle" placeholder="Email" value="<%= handle %>" />
      <input type="password" name="newpass" id="newpass" placeholder="Enter New Password" />
      <input type="password" name="newpass-verify" id="newpass-verify" placeholder="Confirm New Password" />
      <input type="submit" name="submit" id="submit" value="Submit" />
    </form>

  </div>
  <script>
    $(document).ready(function () {

      $("#changepass-form").submit(function (e) {
        e.preventDefault();
        if ($("#newpass").val() != $("#newpass-verify").val()) {
          $("#alert").slideDown();
          $("#newpass").focus();
          return false;
        }
        $.ajax({
          method: "POST",
          type: "POST",
          url: "/api/v1/resetpassword",
          data: {
            email: $("#handle").val(),
            token: $("#token").val(),
            password: $("#newpass").val()
          },
          success: function (resp) {
            console.log(resp);
            if (resp.error) {
              $("#changepass-form").hide();
              $("#error-reason").html(resp.message);
              $("#error-msg").show();
            } else {
              $("#changepass-form").hide();
              $("#success-msg").show();
            }
          }
        })
      })

    })
  </script> -->


