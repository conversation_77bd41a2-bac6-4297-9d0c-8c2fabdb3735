$(document).ready(function () {
  // Clear error messages when user starts typing
  $("#newpass, #newpass-verify").on('input', function() {
    $("#newpass-error, #confirm-error").hide();
  });

  $("#changepass-form").submit(function (e) {
    e.preventDefault();
    
    // Clear previous errors
    $("#newpass-error, #confirm-error").hide();
    
    // Validation
    let isValid = true;
    
    if ($("#newpass").val().length < 6) {
      $("#newpass-error").text("Password must be at least 6 characters long").show();
      isValid = false;
    }
    
    if ($("#newpass").val() != $("#newpass-verify").val()) {
      $("#confirm-error").text("Passwords must match").show();
      isValid = false;
    }
    
    if (!isValid) {
      return false;
    }

    // Disable submit button during request
    $("#submit").prop('disabled', true).val('Resetting...');
    
    $.ajax({
      method: "POST",
      type: "POST",
      url: "/api/v1/resetpassword",
      data: {
        email: $("#handle").val(),
        token: $("#token").val(),
        password: $("#newpass").val()
      },
      success: function (resp) {
        console.log(resp);
        if (resp.error) {
          $("#error-reason").html(resp.message);
          $("#error-msg").addClass('show');
        } else {
          $("#success-msg").addClass('show');
        }
      },
      error: function(xhr, status, error) {
        $("#error-reason").html("An unexpected error occurred. Please try again.");
        $("#error-msg").addClass('show');
      },
      complete: function() {
        // Re-enable submit button
        $("#submit").prop('disabled', false).val('Reset Password');
      }
    });
  });

  // Event handlers for modal buttons
  $("#login-btn").on('click', function() {
    // Redirect to login page
    window.location.href = '/login';
  });

  $("#close-error-btn").on('click', function() {
    $("#error-msg").removeClass('show');
  });

  // Close modals when clicking outside
  $(document).on('click', '.alertMessage', function(e) {
    if (e.target === this) {
      $(this).removeClass('show');
    }
  });
});
